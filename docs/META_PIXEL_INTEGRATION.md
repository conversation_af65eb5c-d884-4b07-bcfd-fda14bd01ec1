# Meta Pixel Integration

This document explains how the Meta Pixel has been integrated into the Kiflow B2B Landing Page.

## Overview

The Meta Pixel (ID: `744906327943825`) has been integrated to track user interactions and conversions on the website. This enables Facebook/Meta advertising optimization and audience building.

## Implementation

### 1. Core Components

- **`components/analytics/MetaPixel.tsx`** - Main Meta Pixel component
- **`types/meta-pixel.d.ts`** - TypeScript declarations for Meta Pixel
- **`app/layout.tsx`** - Root layout integration

### 2. Features

- ✅ Automatic PageView tracking on all pages
- ✅ Custom event tracking utilities
- ✅ Enhanced ActionButton with tracking capabilities
- ✅ TypeScript support
- ✅ Next.js Script optimization

## Usage

### Basic Tracking

The Meta Pixel automatically tracks page views when users visit any page on the site.

### Custom Event Tracking

Use the provided utility functions to track custom events:

```typescript
import { trackEvent, trackCustomEvent } from '@/components/analytics/MetaPixel'

// Track standard Facebook events
trackEvent('Lead', {
  content_name: 'Contact Form',
  value: 1
})

// Track custom events
trackCustomEvent('ButtonClick', {
  button_name: 'Try Now',
  page: 'homepage'
})
```

### ActionButton Tracking

The enhanced ActionButton component now supports automatic tracking:

```tsx
<ActionButton 
  href="https://kiflow-app.expo.app/" 
  trackingEvent="Lead"
  trackingParams={{ 
    content_name: 'Try Now Button',
    value: 1 
  }}
>
  Try Now
</ActionButton>
```

## Standard Events

Common Facebook standard events you can track:

- `PageView` - Automatically tracked
- `Lead` - When someone shows interest
- `CompleteRegistration` - When someone signs up
- `Contact` - When someone contacts you
- `ViewContent` - When someone views important content

## Custom Events

You can also track custom events specific to your business:

- `ButtonClick` - Track specific button interactions
- `FormSubmit` - Track form submissions
- `VideoPlay` - Track video engagement
- `DownloadStart` - Track file downloads

## Verification

To verify the Meta Pixel is working:

1. Install the Meta Pixel Helper browser extension
2. Visit your website
3. Check that the pixel fires correctly
4. Use Facebook Events Manager to see real-time events

## Privacy Considerations

- The pixel respects user privacy settings
- Consider implementing cookie consent if required by your jurisdiction
- The pixel only tracks anonymous user behavior for advertising optimization

## Troubleshooting

If events aren't showing up:

1. Check browser console for JavaScript errors
2. Verify the pixel ID is correct
3. Ensure the Meta Pixel Helper shows the pixel is active
4. Check Facebook Events Manager for event data (may take a few minutes to appear)
