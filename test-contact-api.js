// Simple test script for contact API route
async function testContactAPI() {
  try {
    // Test data
    const testData = {
      name: "Test User",
      company: "Test Company",
      contact: "<EMAIL>"
    };
    
    // Send POST request to the API route
    const response = await fetch('http://localhost:3000/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });
    
    // Parse and log the response
    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', result);
    
    return result;
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

// Run the test
testContactAPI();
