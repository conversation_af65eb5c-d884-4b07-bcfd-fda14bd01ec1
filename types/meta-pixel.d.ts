declare global {
  interface Window {
    fbq: {
      (command: 'init', pixelId: string): void
      (command: 'track', eventName: string, parameters?: Record<string, any>): void
      (command: 'trackCustom', eventName: string, parameters?: Record<string, any>): void
      callMethod?: (...args: any[]) => void
      queue?: any[]
      loaded?: boolean
      version?: string
      push?: (...args: any[]) => void
    }
    _fbq?: any
  }
}

export {}
