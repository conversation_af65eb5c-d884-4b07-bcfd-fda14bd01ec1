"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import { ReactNode } from "react"
import { trackEvent } from "@/components/analytics/MetaPixel"

interface ActionButtonProps {
  href: string
  variant?: "default" | "outline"
  className?: string
  size?: "default" | "sm"
  children: ReactNode
  showArrow?: boolean
  trackingEvent?: string
  trackingParams?: Record<string, any>
}

export function ActionButton({
  href,
  variant = "default",
  className,
  size = "default",
  children,
  showArrow = false,
  trackingEvent,
  trackingParams
}: ActionButtonProps) {
  // Check if the link is external
  const isExternalLink = href.startsWith('http');

  const handleClick = () => {
    if (trackingEvent) {
      trackEvent(trackingEvent, {
        button_text: typeof children === 'string' ? children : 'Button clicked',
        destination_url: href,
        is_external: isExternalLink,
        ...trackingParams
      });
    }
  };

  return (
    <Link
      href={href}
      passHref
      {...(isExternalLink ? { target: "_blank", rel: "noopener noreferrer" } : {})}
      onClick={handleClick}
    >
      <Button variant={variant} className={className} size={size}>
        {children}
        {showArrow && <ArrowRight className="ml-2 h-5 w-5" />}
      </Button>
    </Link>
  )
}
