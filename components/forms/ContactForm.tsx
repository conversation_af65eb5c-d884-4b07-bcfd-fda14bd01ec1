"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Users, BarChart, Check, XCircle, Loader2 } from "lucide-react"

type FormData = {
  name: string
  company: string
  contact: string
}

export function ContactForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')
  
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<FormData>()
  
  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    setSubmitStatus('idle')
    setErrorMessage('')
    
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: data.name,
          company: data.company,
          contact: data.contact
        })
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || 'Щось пішло не так')
      }
      
      setSubmitStatus('success')
      reset() // Clear the form
      
      // Reset success message after 5 seconds
      setTimeout(() => {
        setSubmitStatus('idle')
      }, 5000)
      
    } catch (error: any) {
      setSubmitStatus('error')
      setErrorMessage(error.message || 'Сталася помилка при відправці')
      
      // Reset error message after 5 seconds
      setTimeout(() => {
        setSubmitStatus('idle')
      }, 5000)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <div className="max-w-md mx-auto bg-white text-black p-8 rounded-xl shadow-xl">
      <h3 className="text-2xl font-bold mb-6">Залиш заявку:</h3>
      
      {submitStatus === 'success' && (
        <div className="mb-6 p-3 bg-green-50 border border-green-200 text-green-700 rounded-md flex items-center">
          <Check className="h-5 w-5 mr-2" />
          <span>Заявку успішно відправлено!</span>
        </div>
      )}
      
      {submitStatus === 'error' && (
        <div className="mb-6 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center">
          <XCircle className="h-5 w-5 mr-2" />
          <span>{errorMessage || 'Сталася помилка'}</span>
        </div>
      )}
      
      <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="relative">
          <Input
            {...register('name', { required: "Необхідно вказати ім'я" })}
            type="text"
            placeholder="Ім'я"
            className={`w-full p-4 text-lg rounded-xl pl-12 ${errors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-200 focus:border-black focus:ring-black'}`}
            disabled={isSubmitting}
          />
          <Users className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>
        
        <div className="relative">
          <Input
            {...register('company', { required: "Необхідно вказати компанію" })}
            type="text"
            placeholder="Назва компанії"
            className={`w-full p-4 text-lg rounded-xl pl-12 ${errors.company ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-200 focus:border-black focus:ring-black'}`}
            disabled={isSubmitting}
          />
          <BarChart className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          {errors.company && (
            <p className="text-red-500 text-sm mt-1">{errors.company.message}</p>
          )}
        </div>
        
        <div className="relative">
          <Input
            {...register('contact', { required: "Необхідно вказати контактні дані" })}
            type="text"
            placeholder="Телефон або Telegram"
            className={`w-full p-4 text-lg rounded-xl pl-12 ${errors.contact ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-200 focus:border-black focus:ring-black'}`}
            disabled={isSubmitting}
          />
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
              <circle cx="12" cy="13" r="4"></circle>
            </svg>
          </div>
          {errors.contact && (
            <p className="text-red-500 text-sm mt-1">{errors.contact.message}</p>
          )}
        </div>
        
        <Button 
          type="submit" 
          className="w-full bg-black hover:bg-gray-800 text-white text-lg py-6 rounded-xl"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Відправка...
            </>
          ) : (
            'Надіслати заявку'
          )}
        </Button>
      </form>
    </div>
  )
}
