import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  CheckCircle,
  Users,
  Brain,
  Clock,
  BarChart,
  Zap,
  MessageSquare,
  FileText,
  Layers,
  Award,
  Smartphone,
  BookOpen,
  Monitor,
  Headphones,
  Settings,
  Shield
} from "lucide-react"
import MobileMenu from "@/components/mobile-menu"
import { ActionButton } from "@/components/ui/action-button"
import { ContactForm } from "@/components/forms/ContactForm"

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="container mx-auto py-6 px-4 md:px-6 relative">
        <div className="flex justify-between items-center">
          <div className="w-32 md:w-40">
            <Image src="/kiflow-logo.jpeg" alt="Kiflow Logo" width={160} height={50} className="w-full" />
          </div>

          <div className="flex items-center">
            <ActionButton 
              href="https://kiflow-app.expo.app/" 
              className="bg-black hover:bg-gray-800 text-white rounded-full px-6"
            >
              Спробувати Зараз
            </ActionButton>
            
            {/* Мобільне меню */}
            <MobileMenu />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section id="hero" className="relative py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 z-0"></div>
        <div className="absolute top-0 right-0 w-1/3 h-full bg-black opacity-5 skew-x-12 transform translate-x-20 z-0"></div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6 leading-tight">
                Навчання відділу продажу через{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-gray-800 to-black">
                  Штучний Інтелект
                </span>
              </h1>
              <p className="text-lg md:text-xl mb-8 text-gray-700 leading-relaxed">
                Ми записуємо, як ви реально продаєте. Створюємо відеоуроки, кейси, GPT-фідбек. Команда вчиться щодня —
                без тренера, без втрати якості.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <ActionButton 
                  href="https://kiflow-app.expo.app/" 
                  className="bg-black hover:bg-gray-800 text-white text-lg py-6 px-8 rounded-full shadow-lg hover:shadow-xl transition-all"
                  showArrow
                >
                  Спробувати Безкоштовно
                </ActionButton>
                <ActionButton
                  href="#cta"
                  variant="outline"
                  className="border-black text-black text-lg py-6 px-8 rounded-full hover:bg-gray-100"
                >
                  Дізнатися більше
                </ActionButton>
              </div>

            </div>
            <div className="relative">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl blur opacity-50"></div>
              <div className="relative rounded-2xl overflow-hidden shadow-2xl border border-gray-200 bg-white">
                {/* Ілюстрація AI навчання */}
                <div className="p-6 flex flex-col h-full">
                  {/* Анімований заголовок */}
                  <div className="flex items-center mb-8">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-black to-gray-700 flex items-center justify-center text-white">
                      <Brain className="h-6 w-6" />
                    </div>
                    <div className="ml-4">
                      <div className="font-medium text-lg">AI Тренер</div>
                      <div className="text-sm text-gray-500">Персоналізоване навчання</div>
                    </div>
                  </div>
                  
                  {/* Процес навчання - візуалізація */}
                  <div className="space-y-6 mb-8">
                    {/* Етап 1: Запис реальних розмов */}
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                          <circle cx="12" cy="13" r="4"></circle>
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Запис розмов</p>
                        <p className="text-sm text-gray-500">Фіксуємо реальні продажі вашої команди</p>
                      </div>
                    </div>
                    
                    {/* Етап 2: AI аналіз */}
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 mr-3">
                        <Brain className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">AI аналіз</p>
                        <p className="text-sm text-gray-500">Система визначає найкращі практики</p>
                      </div>
                    </div>
                    
                    {/* Етап 3: Створення матеріалів */}
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 mr-3">
                        <FileText className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Створення контенту</p>
                        <p className="text-sm text-gray-500">Автоматичне формування навчальних матеріалів</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Кнопки взаємодії */}
                  <div className="mt-auto flex justify-between">
                    <ActionButton 
                      href="https://kiflow-app.expo.app/" 
                      size="sm" 
                      className="rounded-full bg-black text-white w-full"
                    >
                      Спробувати
                    </ActionButton>
                  </div>
                </div>
              </div>
              {/* Збережемо позначку швидкості, але зробимо її більш інтегрованою */}
              <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-gradient-to-br from-black to-gray-800 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                <div className="text-center">
                  <div className="text-2xl">4-6×</div>
                  <div className="text-xs">швидше</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* For Who Section */}
      <section id="for-who" className="py-20 md:py-32 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm font-medium rounded-full mb-4">
              Наші клієнти
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Для кого це рішення</h2>
            <p className="text-lg text-gray-600">
              Наша система адаптується під специфіку вашого бізнесу та допомагає навчати команду продажів ефективно.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: <Monitor size={32} />,
                title: "Девелопери та забудовники",
                text: "Навчання менеджерів з продажу нерухомості та консультантів",
              },
              {
                icon: <Headphones size={32} />,
                title: "Клініки та медичний сервіс",
                text: "Підготовка адміністраторів та медичних консультантів",
              },
              {
                icon: <Smartphone size={32} />,
                title: "Салони та шоуруми",
                text: "Навчання персоналу ефективним продажам та сервісу",
              },
              {
                icon: <Users size={32} />,
                title: "Дистриб'ютори",
                text: "Підготовка торгових команд та представників",
              },
              {
                icon: <BookOpen size={32} />,
                title: "Онлайн-школи",
                text: "Навчання менеджерів з продажу освітніх продуктів",
              },
              {
                icon: <Settings size={32} />,
                title: "Агенції та консалтинг",
                text: "Підготовка консультантів та менеджерів з продажу послуг",
              },
            ].map((item, index) => (
              <Card
                key={index}
                className="p-8 flex flex-col h-full hover:shadow-xl transition-shadow border-0 shadow-md rounded-xl overflow-hidden group"
              >
                <div className="p-4 bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-black group-hover:text-white transition-colors">
                  {item.icon}
                </div>
                <h3 className="text-xl font-bold mb-3">{item.title}</h3>
                <p className="text-gray-600 flex-grow">{item.text}</p>
              </Card>
            ))}
          </div>

          <div className="mt-16 text-center">
            <p className="text-lg font-medium bg-gray-50 inline-block px-6 py-3 rounded-full">
              Якщо у вас є люди, які продають і говорять з клієнтами — це для вас.
            </p>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section id="problems" className="py-20 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white to-transparent"></div>
        <div className="absolute -left-64 top-1/4 w-96 h-96 bg-gray-200 rounded-full opacity-50 blur-3xl"></div>
        <div className="absolute -right-64 bottom-1/4 w-96 h-96 bg-gray-200 rounded-full opacity-50 blur-3xl"></div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm font-medium rounded-full mb-4">
              Виклики
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Типові проблеми, які ми вирішуємо</h2>
            <p className="text-lg text-gray-600">
              Наша система допомагає подолати ці перешкоди та створити ефективну команду продажів.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                icon: <Clock />,
                text: "Новачки входять у роботу понад 30 днів",
              },
              {
                icon: <MessageSquare />,
                text: "Кожен говорить як вміє — клієнт не розуміє, кому довіряти",
              },
              {
                icon: <Users />,
                text: "Керівник пояснює одне й те саме по колу",
              },
              {
                icon: <Award />,
                text: 'Продажі тримаються на "золотих людях", а не на системі',
              },
              {
                icon: <FileText />,
                text: "Менеджери не знають скриптів або не використовують їх",
              },
              {
                icon: <MessageSquare />,
                text: 'Не вміють закривати заперечення типу "дорого" або "подумаю"',
              },
              {
                icon: <Users />,
                text: "Тренери дорогі, ефект короткий",
              },
              {
                icon: <BookOpen />,
                text: "Онлайн-курси без фідбеку — не працюють",
              },
              {
                icon: <Brain />,
                text: "Менеджери не знають конкретних технік і не тренуються на практиці",
              },
            ].map((problem, index) => (
              <div
                key={index}
                className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100"
              >
                <div className="bg-gray-50 rounded-full p-2 mt-1 flex-shrink-0">{problem.icon}</div>
                <p className="text-lg">{problem.text}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 md:py-32 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm font-medium rounded-full mb-4">
              Процес
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Що ми робимо</h2>
            <p className="text-lg text-gray-600">
              Наш підхід поєднує ваш досвід з передовими технологіями штучного інтелекту.
            </p>
          </div>

          <div className="space-y-24">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
              <div className="md:col-span-5 relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-gray-200 to-gray-100 rounded-2xl blur opacity-75"></div>
                <div className="relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                  <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
                    1
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Записуємо досвід вашої компанії</h3>
                  <p className="text-gray-600 mb-6">
                    Ми проводимо глибинні інтерв'ю з вашими найкращими спеціалістами, щоб зафіксувати унікальні підходи
                    та методики.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">
                        Проводимо 2–3 години інтерв'ю з вами, керівником продажів і топами
                      </span>
                    </li>
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">Ви просто відповідаєте на питання — ми все структуруємо</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="md:col-span-7 relative">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-8 h-full">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center text-white mr-4">
                      <MessageSquare className="h-6 w-6" />
                    </div>
                    <div>
                      <h4 className="text-xl font-bold">Інтерв'ю з командою</h4>
                      <p className="text-gray-600">Збір найкращих практик та досвіду</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-300 flex-shrink-0 flex items-center justify-center text-xs font-bold">
                        К
                      </div>
                      <div className="bg-white p-3 rounded-lg rounded-tl-none shadow-sm">
                        <p className="text-gray-800">Як ви зазвичай закриваєте заперечення клієнтів?</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-800 flex-shrink-0 flex items-center justify-center text-white text-xs font-bold">
                        М
                      </div>
                      <div className="bg-gray-800 text-white p-3 rounded-lg rounded-tl-none shadow-sm">
                        <p>
                          Ми використовуємо техніку "відчути, визнати, відповісти"...
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-300 flex-shrink-0 flex items-center justify-center text-xs font-bold">
                        К
                      </div>
                      <div className="bg-white p-3 rounded-lg rounded-tl-none shadow-sm">
                        <p className="text-gray-800">Наведіть приклад успішного кейсу...</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
              <div className="md:col-span-7 md:order-1 order-2 relative">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-8 h-full">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center text-white mr-4">
                      <Layers className="h-6 w-6" />
                    </div>
                    <div>
                      <h4 className="text-xl font-bold">Структурований курс</h4>
                      <p className="text-gray-600">Модулі та уроки для ефективного навчання</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <div className="flex items-center mb-3">
                        <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center text-white mr-3">
                          <BookOpen className="h-4 w-4" />
                        </div>
                      <p className="font-medium">Модуль 1: Основи продажу</p>
                      </div>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p>• Встановлення контакту</p>
                        <p>• Виявлення потреб</p>
                        <p>• Презентація продукту</p>
                        <p>• Закриття угоди</p>
                      </div>
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" className="rounded-full text-xs">
                          4 уроки
                        </Button>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <div className="flex items-center mb-3">
                        <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center text-white mr-3">
                          <MessageSquare className="h-4 w-4" />
                        </div>
                      <p className="font-medium">Модуль 2: Заперечення</p>
                      </div>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p>• Типи заперечень</p>
                        <p>• Техніки роботи</p>
                        <p>• Практичні приклади</p>
                      </div>
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" className="rounded-full text-xs">
                          5 уроків
                        </Button>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <div className="flex items-center mb-3">
                        <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center text-white mr-3">
                          <BarChart className="h-4 w-4" />
                        </div>
                      <p className="font-medium">Модуль 3: Закриття угоди</p>
                      </div>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p>• Сигнали готовності</p>
                        <p>• Техніки закриття</p>
                        <p>• Робота з відмовами</p>
                      </div>
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" className="rounded-full text-xs">
                          3 уроки
                        </Button>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <div className="flex items-center mb-3">
                        <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center text-white mr-3">
                          <Users className="h-4 w-4" />
                        </div>
                      <p className="font-medium">Модуль 4: Комунікація</p>
                      </div>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p>• Активне слухання</p>
                        <p>• Невербальні сигнали</p>
                        <p>• Емоційний інтелект</p>
                      </div>
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" className="rounded-full text-xs">
                          6 уроків
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="md:col-span-5 md:order-2 order-1 relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-gray-200 to-gray-100 rounded-2xl blur opacity-75"></div>
                <div className="relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                  <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
                    2
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Робимо з цього курс</h3>
                  <p className="text-gray-600 mb-6">
                    Перетворюємо зібрану інформацію на структурований навчальний матеріал, адаптований під ваш бізнес.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">Розбиваємо інформацію на модулі й уроки</span>
                    </li>
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">У кожному уроці — приклад, кейс, коротка вижимка, ситуація</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
              <div className="md:col-span-5 relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-gray-200 to-gray-100 rounded-2xl blur opacity-75"></div>
                <div className="relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                  <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
                    3
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Створюємо віртуального тренера</h3>
                  <p className="text-gray-600 mb-6">
                    Розробляємо персоналізованого віртуального тренера, який представляє вашу компанію.
                  </p>
                  <ul className="space-y-4">
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">Озвучує навчальні матеріали</span>
                    </li>
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">Говорить мовою вашої компанії</span>
                    </li>
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">Адаптується під специфіку вашого бізнесу</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="md:col-span-7 relative">
                <div className="bg-gradient-to-br from-gray-800 to-black rounded-2xl p-8 h-full text-white">
                  <div className="flex justify-center mb-8">
                    <div className="w-24 h-24 rounded-full bg-gradient-to-br from-gray-700 to-gray-500 flex items-center justify-center">
                      <Users className="h-12 w-12" />
                    </div>
                  </div>
                  <div className="max-w-md mx-auto">
                    <div className="flex justify-center mb-4">
                      <div className="h-2 w-16 bg-gray-600 rounded-full"></div>
                    </div>
                    <div className="text-center mb-6">
                      <h4 className="text-xl font-bold mb-2">Віртуальний тренер</h4>
                      <p className="text-gray-400">Персоналізований під вашу компанію</p>
                    </div>
                    <div className="space-y-4">
                      <p className="text-center text-gray-300">
                        "Віртуальний тренер з продажу допомагає опанувати ефективні техніки закриття угоди у вашій галузі."
                      </p>
                      <p className="text-center text-sm text-gray-400 mt-4">
                        Використовує вашу корпоративну мову, термінологію та приклади
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
              <div className="md:col-span-7 md:order-1 order-2 relative">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-8 h-full">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center text-white mr-4">
                      <Brain className="h-6 w-6" />
                    </div>
                    <div>
                      <h4 className="text-xl font-bold">GPT-наставник</h4>
                      <p className="text-gray-600">Персоналізований зворотний зв'язок</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-300 flex-shrink-0 flex items-center justify-center text-xs font-bold">
                        М
                      </div>
                      <div className="bg-white p-3 rounded-lg rounded-tl-none shadow-sm">
                        <p className="text-gray-800">Як мені відповісти клієнту, який каже що це занадто дорого?</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-800 flex-shrink-0 flex items-center justify-center text-white text-xs font-bold">
                        AI
                      </div>
                      <div className="bg-gray-800 text-white p-3 rounded-lg rounded-tl-none shadow-sm">
                        <p>
                          Спочатку підтвердіть цінність продукту, потім запитайте, яка саме частина ціни викликає
                          сумніви...
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-300 flex-shrink-0 flex items-center justify-center text-xs font-bold">
                        М
                      </div>
                      <div className="bg-white p-3 rounded-lg rounded-tl-none shadow-sm">
                        <p className="text-gray-800">А якщо клієнт каже, що хоче подумати?</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="md:col-span-5 md:order-2 order-1 relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-gray-200 to-gray-100 rounded-2xl blur opacity-75"></div>
                <div className="relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                  <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
                    4
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Підключаємо GPT-наставника</h3>
                  <p className="text-gray-600 mb-6">
                    Інтегруємо штучний інтелект, який надає персоналізований зворотний зв'язок та допомагає
                    вдосконалювати навички.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">Менеджер вирішує кейс → GPT дає точний фідбек</span>
                    </li>
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">
                        Не загальними фразами, а по суті: як сказати краще, чого не вистачає, що змінити
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
              <div className="md:col-span-5 relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-gray-200 to-gray-100 rounded-2xl blur opacity-75"></div>
                <div className="relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                  <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
                    5
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Завантажуємо ваші документи</h3>
                  <p className="text-gray-600 mb-6">
                    Інтегруємо ваші корпоративні матеріали та стандарти в систему навчання.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">Скрипти, стандарти, правила</span>
                    </li>
                    <li className="flex items-start space-x-3">
                      <div className="bg-gray-50 rounded-full p-1 mt-1 flex-shrink-0">
                        <CheckCircle className="text-black h-4 w-4" />
                      </div>
                      <span className="text-gray-700">GPT відповідає і навчає саме так, як заведено у вас</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="md:col-span-7 relative">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-8 h-full">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center text-white mr-4">
                      <FileText className="h-6 w-6" />
                    </div>
                    <div>
                      <h4 className="text-xl font-bold">Корпоративні документи</h4>
                      <p className="text-gray-600">Інтеграція ваших стандартів та правил</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white p-4 rounded-lg shadow-sm transform rotate-[-3deg]">
                      <div className="flex items-center mb-3">
                        <FileText className="h-5 w-5 text-gray-700 mr-2" />
                        <p className="font-medium">Скрипт продажу</p>
                      </div>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p>1. Привітання клієнта</p>
                        <p>2. Виявлення потреб</p>
                        <p>3. Презентація рішення</p>
                        <p>4. Закриття угоди</p>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm transform rotate-[3deg]">
                      <div className="flex items-center mb-3">
                        <FileText className="h-5 w-5 text-gray-700 mr-2" />
                        <p className="font-medium">Стандарти сервісу</p>
                      </div>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p>• Швидкість відповіді</p>
                        <p>• Чіткість комунікації</p>
                        <p>• Робота з запереченнями</p>
                        <p>• Післяпродажний супровід</p>
                      </div>
                    </div>
                    <div className="col-span-2 bg-white p-4 rounded-lg shadow-sm">
                      <div className="flex items-center mb-3">
                        <Shield className="h-5 w-5 text-gray-700 mr-2" />
                        <p className="font-medium">Безпека даних</p>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        Всі ваші корпоративні матеріали захищені та використовуються тільки для навчання ваших співробітників
                      </p>
                      <div className="flex items-center text-xs text-black font-medium">
                        <CheckCircle className="h-4 w-4 mr-1 text-black" />
                        <span>Шифрування</span>
                        <CheckCircle className="h-4 w-4 mx-1 text-black" />
                        <span>NDA</span>
                        <CheckCircle className="h-4 w-4 mx-1 text-black" />
                        <span>Обмежений доступ</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Case Section */}
      <section id="use-case" className="py-20 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="absolute -left-32 top-1/3 w-64 h-64 bg-gray-200 rounded-full opacity-50 blur-3xl"></div>
        <div className="absolute -right-32 bottom-1/3 w-64 h-64 bg-gray-200 rounded-full opacity-50 blur-3xl"></div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm font-medium rounded-full mb-4">
              Приклад
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">User Case — простими словами</h2>
            <p className="text-lg text-gray-600">
              Ось як наша система працює на практиці.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-gray-200 to-gray-100 rounded-2xl blur opacity-75"></div>
              <div className="relative bg-white p-10 rounded-2xl shadow-lg border border-gray-100">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mr-6">
                    <Users className="h-8 w-8 text-gray-500" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold">Новий менеджер у команді</h3>
                    <p className="text-gray-600">Від новачка до ефективного продавця</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                  <div className="bg-gray-50 p-6 rounded-xl">
                    <h4 className="text-lg font-bold mb-4 text-gray-400">До впровадження Kiflow</h4>
                    <p className="text-gray-600 mb-4">
                      Раніше новому менеджеру знадобився би місяць, щоб "втягнутись", і ще один — щоб почати продавати.
                    </p>
                    <div className="flex flex-col space-y-3 mt-6">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                          <Clock className="h-5 w-5 text-gray-400" />
                        </div>
                        <div className="text-gray-400">2 місяці навчання</div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                          <MessageSquare className="h-5 w-5 text-gray-400" />
                        </div>
                        <div className="text-gray-400">Постійні повторення одного й того ж</div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                          <BarChart className="h-5 w-5 text-gray-400" />
                        </div>
                        <div className="text-gray-400">Низька конверсія перші 3 місяці</div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-black p-6 rounded-xl">
                    <h4 className="text-lg font-bold mb-4 text-white">З Kiflow</h4>
                    <p className="text-gray-300 mb-4">
                      Зараз він проходить навчальний курс, виконує практичні кейси, отримує фідбек — і вже на 5-й день
                      проводить показ і впевнено спілкується з клієнтом.
                    </p>
                    <div className="flex flex-col space-y-3 mt-6">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center mr-4">
                          <Zap className="h-5 w-5 text-white" />
                        </div>
                        <div className="text-white">5 днів до повноцінної роботи</div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center mr-4">
                          <Brain className="h-5 w-5 text-white" />
                        </div>
                        <div className="text-white">Персоналізоване навчання від AI</div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center mr-4">
                          <BarChart className="h-5 w-5 text-white" />
                        </div>
                        <div className="text-white">Висока конверсія з перших тижнів</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-6 rounded-xl">
                  <div className="flex items-start space-x-4">
                    <div className="bg-black rounded-full p-2 mt-1 flex-shrink-0">
                      <CheckCircle className="text-white h-5 w-5" />
                    </div>
                    <div>
                      <p className="text-lg mb-2">Керівник не витрачає на це ні хвилини.</p>
                      <p className="text-gray-600">
                        Досвідчені менеджери теж проходять модулі — і кожен починає говорити в єдиному стилі.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 md:py-32 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm font-medium rounded-full mb-4">
              Переваги
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Що отримає ваша компанія</h2>
            <p className="text-lg text-gray-600">
              Впровадження Kiflow дає вашому бізнесу конкретні вимірювані результати.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="p-8 hover:shadow-xl transition-shadow border-0 shadow-md rounded-xl overflow-hidden group">
              <div className="p-4 bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-black group-hover:text-white transition-colors">
                <Zap size={32} />
              </div>
              <h3 className="text-xl font-bold mb-3">Швидкий онбординг</h3>
              <p className="text-gray-600 mb-4">Онбординг новачків — в 4–6 разів швидше</p>
              <div className="flex items-center mt-auto">
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div className="bg-black h-2 rounded-full" style={{ width: '80%' }}></div>
                </div>
                <span className="ml-2 text-sm font-medium">80%</span>
              </div>
            </Card>
            
            <Card className="p-8 hover:shadow-xl transition-shadow border-0 shadow-md rounded-xl overflow-hidden group">
              <div className="p-4 bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-black group-hover:text-white transition-colors">
                <Clock size={32} />
              </div>
              <h3 className="text-xl font-bold mb-3">Економія часу</h3>
              <p className="text-gray-600 mb-4">Мінус 80% повторів і пояснень для керівника</p>
              <div className="flex items-center mt-auto">
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div className="bg-black h-2 rounded-full" style={{ width: '75%' }}></div>
                </div>
                <span className="ml-2 text-sm font-medium">75%</span>
              </div>
            </Card>
            
            <Card className="p-8 hover:shadow-xl transition-shadow border-0 shadow-md rounded-xl overflow-hidden group">
              <div className="p-4 bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-black group-hover:text-white transition-colors">
                <Users size={32} />
              </div>
              <h3 className="text-xl font-bold mb-3">Єдиний стиль</h3>
              <p className="text-gray-600 mb-4">Єдиний стиль комунікації в команді</p>
              <div className="flex items-center mt-auto">
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div className="bg-black h-2 rounded-full" style={{ width: '90%' }}></div>
                </div>
                <span className="ml-2 text-sm font-medium">90%</span>
              </div>
            </Card>
            
            <Card className="p-8 hover:shadow-xl transition-shadow border-0 shadow-md rounded-xl overflow-hidden group">
              <div className="p-4 bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-black group-hover:text-white transition-colors">
                <Brain size={32} />
              </div>
              <h3 className="text-xl font-bold mb-3">Персоналізований фідбек</h3>
              <p className="text-gray-600 mb-4">Коментарі GPT до кожної відповіді продавця</p>
              <div className="flex items-center mt-auto">
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div className="bg-black h-2 rounded-full" style={{ width: '95%' }}></div>
                </div>
                <span className="ml-2 text-sm font-medium">95%</span>
              </div>
            </Card>
            
            <Card className="p-8 hover:shadow-xl transition-shadow border-0 shadow-md rounded-xl overflow-hidden group">
              <div className="p-4 bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-black group-hover:text-white transition-colors">
                <Shield size={32} />
              </div>
              <h3 className="text-xl font-bold mb-3">Безпека даних</h3>
              <p className="text-gray-600 mb-4">Повний контроль над корпоративною інформацією</p>
              <div className="flex items-center mt-auto">
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div className="bg-black h-2 rounded-full" style={{ width: '100%' }}></div>
                </div>
                <span className="ml-2 text-sm font-medium">100%</span>
              </div>
            </Card>
            
            <Card className="p-8 hover:shadow-xl transition-shadow border-0 shadow-md rounded-xl overflow-hidden group">
              <div className="p-4 bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-black group-hover:text-white transition-colors">
                <BarChart size={32} />
              </div>
              <h3 className="text-xl font-bold mb-3">Ріст продажів</h3>
              <p className="text-gray-600 mb-4">Збільшення конверсії на 25-40%</p>
              <div className="flex items-center mt-auto">
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div className="bg-black h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
                <span className="ml-2 text-sm font-medium">85%</span>
              </div>
            </Card>
          </div>

          <div className="mt-12 text-center">
            <div className="inline-flex items-center bg-gray-50 px-6 py-3 rounded-full">
              <BarChart className="h-5 w-5 mr-2 text-black" />
              <span className="text-lg font-medium">10 разів дешевше, ніж запрошувати тренерів щокварталу</span>
            </div>
          </div>
        </div>
      </section>

      {/* Guarantee Section */}
      <section id="guarantee" className="py-20 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="absolute -left-32 top-1/3 w-64 h-64 bg-gray-200 rounded-full opacity-50 blur-3xl"></div>
        <div className="absolute -right-32 bottom-1/3 w-64 h-64 bg-gray-200 rounded-full opacity-50 blur-3xl"></div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto">
            <div className="relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-gray-200 to-gray-100 rounded-2xl blur opacity-75"></div>
              <div className="relative bg-white p-10 rounded-2xl shadow-lg border border-gray-100 text-center">
                <div className="w-20 h-20 bg-black rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="h-10 w-10 text-white" />
                </div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">А якщо не спрацює?</h2>
                <p className="text-xl mb-6">Ми не продаємо "курс". Ми впроваджуємо систему.</p>
                <div className="inline-block bg-black text-white text-xl font-bold px-8 py-4 rounded-full">
                  Якщо за перший місяць ви не побачите змін — ми повертаємо кошти.
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="cta" className="py-20 md:py-32 bg-black text-white relative overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-gray-900 to-transparent"></div>
        <div className="absolute -left-32 top-1/3 w-64 h-64 bg-gray-800 rounded-full opacity-30 blur-3xl"></div>
        <div className="absolute -right-32 bottom-1/3 w-64 h-64 bg-gray-800 rounded-full opacity-30 blur-3xl"></div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <div className="inline-block px-3 py-1 bg-gray-800 text-gray-200 text-sm font-medium rounded-full mb-4">
              Почніть зараз
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Хочете побачити, як це працює у вас?</h2>
            <p className="text-xl text-gray-300 mb-10 max-w-3xl mx-auto">
              Залиште заявку — ми зв'яжемось на короткий 10-хвилинний дзвінок, розповімо про можливості й умови
              впровадження Kiflow саме для вашої компанії.
            </p>
            <ActionButton 
              href="https://kiflow-app.expo.app/" 
              className="bg-white hover:bg-gray-200 text-black text-lg py-6 px-10 rounded-full shadow-lg hover:shadow-xl transition-all mb-16"
              showArrow
            >
              Спробувати
            </ActionButton>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white py-8 border-t border-gray-100">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 mb-4 md:mb-0">&#169; {new Date().getFullYear()} Kiflow. Всі права захищені.</p>
            <div className="flex space-x-5">
              <a href="https://www.facebook.com/deraga.roman" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-black transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
              <a href="https://www.instagram.com/deregaroman/" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-black transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
              <a href="https://www.linkedin.com/in/roman-dereha-13a9b3305/" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-black transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                  <rect x="2" y="9" width="4" height="12"></rect>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
